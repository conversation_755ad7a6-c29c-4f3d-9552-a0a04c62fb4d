import { FC } from 'react'
import { Modal, Form, Input, But<PERSON>, Alert, Typography } from 'antd'
import { UserOutlined, LockOutlined, CloseOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useAuth } from '../hooks/useAuth'

const { Title, Text } = Typography

interface LoginModalProps {
  visible: boolean
  onClose: () => void
  onSwitchToRegister: () => void
}

export const LoginModal: FC<LoginModalProps> = ({
  visible,
  onClose,
  onSwitchToRegister
}) => {
  const { t } = useTranslation(['common', 'auth'])
  const { signIn, loading, error, clearError, user } = useAuth()
  const [form] = Form.useForm()

  const handleSubmit = async (values: { email: string; password: string }) => {
    const { success } = await signIn(values.email, values.password)
    if (success) {
      form.resetFields()
      onClose()
    }
  }

  const handleCancel = () => {
    clearError()
    form.resetFields()
    onClose()
  }

  return (
    <Modal
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={400}
      centered
      closable={true}
      maskClosable={true}
      closeIcon={<CloseOutlined style={{ fontSize: '16px', color: '#6b7280' }} />}
    >
      <div style={{ textAlign: 'center', marginBottom: '32px' }}>
        <Title level={2} style={{
          color: '#2563eb',
          marginBottom: '8px',
          fontSize: '28px'
        }}>
          {t('auth:login.title')}
        </Title>
        <Text type="secondary" style={{ fontSize: '14px' }}>
          {t('auth:login.subtitle')}
        </Text>
      </div>

      {error && (
        <Alert
          message={error}
          type="error"
          showIcon
          style={{ marginBottom: '24px' }}
          closable
          onClose={clearError}
        />
      )}

      <Form
        form={form}
        onFinish={handleSubmit}
        layout="vertical"
        size="large"
      >
        <Form.Item
          name="email"
          label={t('auth:form.email')}
          rules={[
            { required: true, message: t('auth:validation.emailRequired') },
            { type: 'email', message: t('auth:validation.emailInvalid') }
          ]}
        >
          <Input
            prefix={<UserOutlined style={{ color: '#bfbfbf' }} />}
            placeholder={t('auth:placeholder.email')}
            autoComplete="email"
          />
        </Form.Item>

        <Form.Item
          name="password"
          label={t('auth:form.password')}
          rules={[
            { required: true, message: t('auth:validation.passwordRequired') },
            { min: 6, message: t('auth:validation.passwordMinLength') }
          ]}
        >
          <Input.Password
            prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
            placeholder={t('auth:placeholder.password')}
            autoComplete="current-password"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            style={{
              width: '100%',
              height: '44px',
              fontSize: '16px',
              fontWeight: '500',
              backgroundColor: '#2563eb',
              borderColor: '#2563eb'
            }}
          >
            {t('auth:login.submit')}
          </Button>
        </Form.Item>

        {!user?.email_confirmed_at && (
          <div style={{ textAlign: 'center', marginTop: '16px' }}>
            <Text type="secondary" style={{ fontSize: '14px' }}>
              {t('auth:login.noAccount')}{' '}
              <Button
                type="link"
                onClick={onSwitchToRegister}
                style={{
                  padding: 0,
                  height: 'auto',
                  color: '#2563eb',
                  fontSize: '14px',
                  fontWeight: '500'
                }}
              >
                {t('auth:login.registerNow')}
              </Button>
            </Text>
          </div>
        )}
      </Form>
    </Modal>
  )
}