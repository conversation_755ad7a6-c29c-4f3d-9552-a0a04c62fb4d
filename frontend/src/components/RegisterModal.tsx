import { FC, useState, useEffect } from 'react'
import { Modal, Form, Input, Button, Alert, Typography } from 'antd'
import { LockOutlined, MailOutlined, CloseOutlined, MailTwoTone } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'
import { useAuth } from '../hooks/useAuth'

const { Title, Text } = Typography

interface RegisterModalProps {
  visible: boolean
  onClose: () => void
  onSwitchToLogin: () => void
}

export const RegisterModal: FC<RegisterModalProps> = ({
  visible,
  onClose,
  onSwitchToLogin
}) => {
  const { t } = useTranslation(['common', 'auth'])
  const { signUp, loading, error, clearError, user, resendVerificationEmail } = useAuth()
  const [form] = Form.useForm()
  const [registrationStep, setRegistrationStep] = useState<'form' | 'verification_sent'>('form')
  const [emailError, setEmailError] = useState<string | null>(null)

  const handleSubmit = async (values: {
    email: string
    password: string
    confirmPassword: string
  }) => {
    // 清除之前的邮箱错误
    setEmailError(null)

    // 直接尝试注册，根据结果处理不同情况
    const { success, error } = await signUp(values.email, values.password)

    if (success) {
      // 情况1：注册成功（新邮箱或邮箱存在但未验证）
      form.resetFields()
      setRegistrationStep('verification_sent')
    } else if (error) {
      // 分析错误信息来判断具体情况
      if (error.includes('already registered') ||
          error.includes('already exists') ||
          error.includes('User already registered') ||
          error.includes('already been registered')) {
        // 情况3：邮箱已存在且已验证，显示红色错误提示，不发送邮件
        setEmailError('该邮箱已注册')
      } else if (error.includes('email not confirmed') ||
                 error.includes('not confirmed')) {
        // 情况2：邮箱存在但未验证，重新发送验证邮件
        await resendVerificationEmail(values.email)
        form.resetFields()
        setRegistrationStep('verification_sent')
      } else {
        // 其他错误，显示具体错误信息
        // 这里不设置 emailError，让 useAuth 中的 error 状态处理
      }
    }
  }

  const handleCancel = () => {
    clearError()
    setEmailError(null)
    form.resetFields()
    setRegistrationStep('form')
    onClose()
  }

  const handleComplete = () => {
    setEmailError(null)
    setRegistrationStep('form')
    onClose()
  }

  const handleResendVerification = async () => {
    if (form.getFieldValue('email')) {
      const { error } = await resendVerificationEmail(form.getFieldValue('email'))
      if (error) {
        // 可以显示错误信息，但为了简化先不显示
        console.error('重发验证邮件失败:', error)
      }
    }
  }

  // 监听认证状态变化，当用户验证邮箱后自动关闭弹窗
  useEffect(() => {
    if (registrationStep === 'verification_sent' && user?.email_confirmed_at) {
      // 用户已验证邮箱，显示成功状态并关闭弹窗
      setTimeout(() => {
        setRegistrationStep('form')
        onClose()
      }, 1500) // 延迟1.5秒关闭，让用户看到成功状态
    }
  }, [registrationStep, user, onClose])

  return (
    <Modal
      open={visible}
      onCancel={registrationStep === 'form' ? handleCancel : handleComplete}
      footer={null}
      width={registrationStep === 'form' ? 400 : 500}
      centered
      closable={true}
      maskClosable={registrationStep === 'form'}
      closeIcon={<CloseOutlined style={{ fontSize: '16px', color: '#6b7280' }} />}
    >
      {/* 注册表单步骤 */}
      {registrationStep === 'form' && (
        <>
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <Title level={2} style={{
              color: '#2563eb',
              marginBottom: '8px',
              fontSize: '28px'
            }}>
              {t('auth:register.title')}
            </Title>
            <Text type="secondary" style={{ fontSize: '14px' }}>
              {t('auth:register.subtitle')}
            </Text>
          </div>

          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              style={{ marginBottom: '24px' }}
              closable
              onClose={clearError}
            />
          )}



          <Form
            form={form}
            onFinish={handleSubmit}
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              label={t('auth:form.email')}
              rules={[
                { required: true, message: t('auth:validation.emailRequired') },
                { type: 'email', message: t('auth:validation.emailInvalid') }
              ]}
              validateStatus={emailError ? 'error' : ''}
              help={emailError}
            >
              <Input
                prefix={<MailOutlined style={{ color: '#bfbfbf' }} />}
                placeholder={t('auth:placeholder.email')}
                autoComplete="email"
                onChange={() => {
                  if (emailError) {
                    setEmailError(null)
                  }
                }}
              />
            </Form.Item>

            <Form.Item
              name="password"
              label={t('auth:form.password')}
              rules={[
                { required: true, message: t('auth:validation.passwordRequired') },
                { min: 6, message: t('auth:validation.passwordMinLength') }
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
                placeholder={t('auth:placeholder.password')}
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item
              name="confirmPassword"
              label={t('auth:form.confirmPassword')}
              dependencies={['password']}
              rules={[
                { required: true, message: t('auth:validation.confirmPasswordRequired') },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error(t('auth:validation.passwordMismatch')))
                  },
                }),
              ]}
            >
              <Input.Password
                prefix={<LockOutlined style={{ color: '#bfbfbf' }} />}
                placeholder={t('auth:placeholder.confirmPassword')}
                autoComplete="new-password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                style={{
                  width: '100%',
                  height: '44px',
                  fontSize: '16px',
                  fontWeight: '500',
                  backgroundColor: '#2563eb',
                  borderColor: '#2563eb'
                }}
              >
                {t('auth:register.submit')}
              </Button>
            </Form.Item>

            <div style={{ textAlign: 'center', marginTop: '16px' }}>
              <Text type="secondary" style={{ fontSize: '14px' }}>
                {t('auth:register.hasAccount')}{' '}
                <Button
                  type="link"
                  onClick={() => {
                    setEmailError(null)
                    onSwitchToLogin()
                  }}
                  style={{
                    padding: 0,
                    height: 'auto',
                    color: '#2563eb',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  {t('auth:register.loginNow')}
                </Button>
              </Text>
            </div>
          </Form>
        </>
      )}

      
      
      {/* 注册成功/验证邮件发送步骤 */}
      {registrationStep === 'verification_sent' && (
        <div style={{ textAlign: 'center', padding: '40px 20px' }}>
          <div style={{ marginBottom: '24px' }}>
            <MailTwoTone 
              twoToneColor="#52c41a" 
              style={{ fontSize: '64px' }} 
            />
          </div>
          
          <Title level={3} style={{ 
            color: '#52c41a', 
            marginBottom: '16px',
            fontSize: '24px'
          }}>
            注册成功！
          </Title>
          
          <Text style={{ 
            fontSize: '16px', 
            color: '#6b7280',
            display: 'block',
            marginBottom: '32px',
            lineHeight: '1.6'
          }}>
            验证邮件已发送到您的邮箱<br />
            请查收邮件并点击验证链接激活账户
          </Text>

          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
            <Button 
              type="primary" 
              onClick={handleResendVerification}
              loading={loading}
              style={{
                backgroundColor: '#2563eb',
                borderColor: '#2563eb'
              }}
            >
              重新发送验证邮件
            </Button>
            
            <Button 
              onClick={handleComplete}
              style={{ marginLeft: '8px' }}
            >
              知道了
            </Button>
          </div>
        </div>
      )}
    </Modal>
  )
}
