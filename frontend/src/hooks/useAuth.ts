import { useState, useEffect } from 'react'
import { authService } from '../services/supabase'

interface User {
  id: string
  email?: string
  [key: string]: any
}

interface AuthState {
  user: User | null
  loading: boolean
  error: string | null
}

export const useAuth = () => {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    loading: true,
    error: null,
  })

  useEffect(() => {
    // 检查当前用户
    const checkUser = async () => {
      try {
        const { user, error } = await authService.getCurrentUser()
        if (error) {
          // 如果是认证相关的错误，不显示为错误，只是设置为未登录状态
          if (error.message.includes('Auth session missing') || error.message.includes('Invalid token')) {
            setAuthState(prev => ({ ...prev, user: null, loading: false }))
          } else {
            setAuthState(prev => ({ ...prev, loading: false, error: error.message }))
          }
        } else {
          setAuthState(prev => ({ ...prev, user, loading: false }))
        }
      } catch (err) {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          user: null // 捕获异常时也设置为未登录状态
        }))
      }
    }

    checkUser()

    // 监听认证状态变化
    const { data: { subscription } } = authService.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          setAuthState(prev => ({ ...prev, user: session.user, loading: false }))
        } else if (event === 'SIGNED_OUT') {
          setAuthState(prev => ({ ...prev, user: null, loading: false }))
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          setAuthState(prev => ({ ...prev, user: session.user, loading: false }))
        }
      }
    )

    return () => {
      subscription?.unsubscribe()
    }
  }, [])



  const resendVerificationEmail = async (email: string) => {
    const { data, error } = await authService.resendVerificationEmail(email)
    return { data, error }
  }

  const signIn = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const { data, error } = await authService.signIn(email, password)

      if (error) {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          error: error.message
        }))
        return { success: false, error: error.message }
      }

      setAuthState(prev => ({
        ...prev,
        user: data.user,
        loading: false
      }))

      return { success: true, user: data.user }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed'
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage }
    }
  }

  const signUp = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const { data, error } = await authService.signUp(email, password)

      if (error) {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          error: error.message
        }))
        return { success: false, error: error.message, user: null }
      }

      return { success: true, error: null, user: data.user }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Registration failed'
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage, user: null }
    }
  }

  const signOut = async () => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const { error } = await authService.signOut()

      if (error) {
        setAuthState(prev => ({
          ...prev,
          loading: false,
          error: error.message
        }))
        return { success: false, error: error.message }
      }

      setAuthState(prev => ({
        ...prev,
        user: null,
        loading: false
      }))

      return { success: true }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Logout failed'
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }))
      return { success: false, error: errorMessage }
    }
  }

  const clearError = () => {
    setAuthState(prev => ({ ...prev, error: null }))
  }

  return {
    ...authState,
    signIn,
    signUp,
    signOut,
    resendVerificationEmail,
    clearError,
  }
}